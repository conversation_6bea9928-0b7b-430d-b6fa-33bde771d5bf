# 税务统计订单同步系统 - MQ消息队列业务逻辑文档

## 1. 概述

### 1.1 文档目标
本文档详细描述了税务统计订单同步系统中所有与消息队列、异步通信、事件处理相关的业务逻辑，以业务流程为主线，涵盖Dubbo RPC通信、XXL-Job任务调度、异步线程池处理、服务发现等核心异步处理机制。

> 📖 **相关文档**: 
> - [业务流程文档](BIZ.md) - 详细的业务流程和代码说明
> - [数据库ER图](ER.md) - 数据库表结构及关系
> - [I/O业务逻辑](IO.md) - 文件读取/写入/存储相关业务逻辑

### 1.2 系统MQ架构概览
系统采用分布式微服务架构，异步通信主要基于以下技术栈：
- **RPC通信**: Dubbo - 服务间远程调用
- **任务调度**: XXL-Job - 分布式任务调度中心
- **服务发现**: Nacos - 服务注册与发现
- **异步处理**: 线程池 - 本地异步任务处理
- **配置管理**: Nacos Config - 动态配置推送
- **日志队列**: Logback AsyncAppender - 异步日志处理

### 1.3 核心MQ业务域
- **订单数据同步**: 通过Dubbo RPC异步同步订单数据
- **任务调度通信**: XXL-Job调度中心与执行器的通信
- **服务间通信**: 微服务之间的异步RPC调用
- **配置变更通知**: Nacos配置变更的推送机制
- **异步任务处理**: 线程池处理耗时操作
- **日志异步写入**: 异步日志队列处理

## 2. Dubbo RPC通信机制

### 2.1 Dubbo服务架构

#### 2.1.1 服务提供者与消费者模式
**架构模式**: 该系统作为Dubbo服务消费者，调用远程税务统计系统的服务

**主启动类配置**: [`StatisticOrderSyncApplication.java`](../src/main/java/cc/buyhoo/tax/StatisticOrderSyncApplication.java)
```java
@SpringBootApplication
@EnableDubbo                    // 启用Dubbo服务
@EnableDiscoveryClient         // 启用服务发现
public class StatisticOrderSyncApplication {
    static {
        // 解决多环境部署时Dubbo缓存文件冲突
        System.setProperty("user.home", "dubboCache");
    }
}
```

#### 2.1.2 服务注册与发现
**注册中心**: Nacos
**配置文件**: [`application.yml`](../src/main/resources/application.yml)
```yaml
spring:
  cloud:
    nacos:
      discovery:
        group: @nacos.tax.group@      # 服务注册组
        namespace: @nacos.namespace@   # 命名空间隔离
```

**配置导入**: 
```yaml
spring:
  config:
    import:
      - optional:nacos:dubbo-config.yaml  # Dubbo专属配置
```

### 2.2 核心Dubbo服务调用

#### 2.2.1 订单同步服务调用
**业务场景**: 将本地订单数据同步到远程税务统计系统

**主要服务接口**:
1. **销售订单同步**: [`OrderSyncCustomTask`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java)
   ```java
   @DubboReference(retries = 0)
   private SaleListFacade saleListFacade;
   
   // 同步销售订单，不重试避免重复数据
   saleListFacade.syncSaleList(syncParams);
   ```

2. **退货订单同步**: 
   ```java
   @DubboReference(retries = 0)
   private ReturnListFacade returnListFacade;
   
   // 同步退货订单
   returnListFacade.syncReturnList(syncParams);
   ```

3. **餐饮订单同步**: [`CanyinOrderSyncCustomTask`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java)
   ```java
   @DubboReference(retries = 0)
   private SaleListFacade saleListFacade;
   
   // 同步餐饮订单
   saleListFacade.syncCanyinSaleList(syncParams);
   ```

#### 2.2.2 商品信息同步服务
**业务场景**: 同步商品基础信息和店铺信息

**核心服务**: [`GoodsSyncTask`](../src/main/java/cc/buyhoo/tax/task/GoodsSyncTask.java)
```java
@DubboReference
private BusShopFacade busShopFacade;

@DubboReference
private GoodsFacade goodsFacade;

@DubboReference
private ShopFacade shopFacade;

// 查询绑定店铺
Result<QueryBindShopResult> shopResult = busShopFacade.queryBindShop();

// 同步商品信息
goodsFacade.syncGoods(companyId, shopUnique, goodsList);

// 同步店铺信息
shopFacade.syncShop(shopList);
```

#### 2.2.3 监控数据同步服务
**业务场景**: 同步订单监控统计数据

**核心服务**: [`SaleListMonitorTask`](../src/main/java/cc/buyhoo/tax/task/SaleListMonitorTask.java)
```java
@DubboReference
private BusShopSaleListMonitorFacade busShopSaleListMonitorFacade;

// 异步更新监控数据
ThreadUtil.execAsync(() -> 
    busShopSaleListMonitorFacade.updateSaleListMonitor(listParams)
);
```

### 2.3 Dubbo通信特性配置

#### 2.3.1 重试策略配置
**无重试配置**: 对于数据同步类服务，避免重复提交
```java
@DubboReference(retries = 0)  // 不重试，避免重复数据
private SaleListFacade saleListFacade;

@DubboReference(retries = 0)
private BusShopInvoiceFacade busShopInvoiceFacade;
```

**默认重试**: 对于查询类服务，允许重试
```java
@DubboReference  // 使用默认重试策略
private SysCompanyFacade sysCompanyFacade;

@DubboReference
private BusShopFacade busShopFacade;
```

#### 2.3.2 服务调用模式
**同步调用**: 大部分业务场景使用同步RPC调用
**异步处理**: 通过线程池实现业务层面的异步处理

```java
// 同步RPC调用 + 异步业务处理
ThreadUtil.execAsync(() -> {
    // 在异步线程中进行RPC调用
    busShopBillFacade.statisticBill(comp.getId(), startDate, endDate);
});
```

## 3. XXL-Job任务调度通信

### 3.1 任务调度架构

#### 3.1.1 调度中心与执行器通信
**通信模式**: XXL-Job调度中心通过HTTP协议与执行器通信
**执行器注册**: 应用启动时自动向调度中心注册

**任务执行器配置**: 通过Nacos配置中心动态配置
- 调度中心地址
- 执行器AppName
- 执行器端口
- 访问令牌

#### 3.1.2 任务注册与发现
**任务注册**: 通过`@XxlJob`注解自动注册任务处理器
**任务发现**: 调度中心自动发现并管理注册的任务

### 3.2 核心调度任务

#### 3.2.1 商品同步任务
**任务标识**: `goodsSyncJobHandler`
**执行类**: [`GoodsSyncTask.goodsSync()`](../src/main/java/cc/buyhoo/tax/task/GoodsSyncTask.java#L62)

```java
@XxlJob("goodsSyncJobHandler")
public void goodsSync() {
    Date startTime = DateUtil.date();
    log.info("------定时任务同步商品信息：开始同步----------------------------");
    
    // 查询店铺列表
    Result<QueryBindShopResult> shopResult = busShopFacade.queryBindShop();
    if (shopResult.hasFail()) {
        XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));
    }
    
    // 异步处理店铺同步
    pool.execute(() -> {
        shopFacade.syncShop(queryShops(shopUniques));
    });
}
```

#### 3.2.2 百货订单同步任务
**任务标识**: `orderSyncCustomJobHandler`
**执行类**: [`OrderSyncCustomTask.orderSync()`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java#L95)

```java
@XxlJob("orderSyncCustomJobHandler")
public void orderSync() {
    // 查询分公司列表
    Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();
    if (branchCompResult.hasFail()) {
        XxlJobHelper.log("查询分公司失败:{}", JSON.toJSONString(branchCompResult));
        return;
    }
    
    // 遍历分公司进行订单同步
    for (QueryBranchCompanyDto comp : companyList) {
        // 同步百货订单数据
        syncBuyhooOrderData(comp, startTime, endTime);
    }
}
```

#### 3.2.3 餐饮订单同步任务
**任务标识**: `canyinOrderSyncJobHandler`
**执行类**: [`CanyinOrderSyncCustomTask.canyinOrderSyncJobHandler()`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java#L128)

```java
@XxlJob("canyinOrderSyncJobHandler")
public void canyinOrderSyncJobHandler() {
    // 查询分公司
    Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();
    if (branchCompResult.hasFail()) {
        XxlJobHelper.log("查询分公司失败:{}", JSON.toJSONString(branchCompResult));
        return;
    }

    // 同步餐饮订单数据
    for (QueryBranchCompanyDto comp : companyList) {
        syncCanyinOrderData(comp, startTime, endTime);
    }
}
```

#### 3.2.4 账单统计任务
**任务标识**: `billStatisticJobHandler`
**执行类**: [`BillStatisticTask.billStatistic()`](../src/main/java/cc/buyhoo/tax/task/BillStatisticTask.java#L38)

```java
@XxlJob("billStatisticJobHandler")
public void billStatistic() {
    String params = XxlJobHelper.getJobParam();
    Date startDate;
    Date endDate;

    // 解析任务参数
    if (ObjectUtil.isNotEmpty(params)) {
        String[] paramsArray = params.split(",");
        startDate = DateUtil.beginOfDay(DateUtil.parse(paramsArray[0])).toJdkDate();
        endDate = DateUtil.endOfDay(DateUtil.parse(paramsArray[1])).toJdkDate();
        XxlJobHelper.log("服务费、结算统计时间区间:" + "[" + params + "]");
    }

    // 异步统计各分公司账单
    for (QueryBranchCompanyDto comp : companyList) {
        ThreadUtil.execAsync(() ->
            busShopBillFacade.statisticBill(comp.getId(), startDate, endDate)
        );
    }
}
```

#### 3.2.5 订单监控任务
**任务标识**: `saleListMonitorJobHandler`
**执行类**: [`SaleListMonitorTask.saleListMonitor()`](../src/main/java/cc/buyhoo/tax/task/SaleListMonitorTask.java#L71)

```java
@XxlJob("saleListMonitorJobHandler")
public void saleListMonitor() {
    Date endTime = DateUtil.endOfDay(DateUtil.yesterday());
    Date startTime = DateUtil.beginOfDay(DateUtil.yesterday());

    // 查询分公司
    Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();
    if (branchCompResult.hasFail()) {
        XxlJobHelper.log("查询分公司失败:{}", JSON.toJSONString(branchCompResult));
        return;
    }

    // 监控各分公司订单数据
    for (QueryBranchCompanyDto comp : companyList) {
        monitorCompanyOrderData(comp, startTime, endTime);
    }
}
```

#### 3.2.6 数据清理任务
**任务标识**: `truncateOrderData`
**执行类**: [`TruncateOrderDataTask.truncateOrderData()`](../src/main/java/cc/buyhoo/tax/task/TruncateOrderDataTask.java#L17)

```java
@XxlJob("truncateOrderData")
public void truncateOrderData() {
    // 清空纳统订单相关数据，方便测试人员测试
    truncateOrderDataFacade.truncateOrdeData();
}
```

### 3.3 任务参数传递与日志

#### 3.3.1 任务参数处理
**参数获取**: 通过`XxlJobHelper.getJobParam()`获取调度中心传递的参数

**参数格式示例**:
- **时间范围参数**: "2024-01-01,2024-01-31" (开始日期,结束日期)
- **空参数**: 使用默认时间范围(如昨天)

```java
String params = XxlJobHelper.getJobParam();
if (ObjectUtil.isNotEmpty(params)) {
    String[] paramsArray = params.split(",");
    startDate = DateUtil.beginOfDay(DateUtil.parse(paramsArray[0])).toJdkDate();
    endDate = DateUtil.endOfDay(DateUtil.parse(paramsArray[1])).toJdkDate();
}
```

#### 3.3.2 任务执行日志
**日志记录**: 通过`XxlJobHelper.log()`记录任务执行日志，可在调度中心查看

**日志示例**:
```java
XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));
XxlJobHelper.log("服务费、结算统计时间区间:" + "[" + params + "]");
XxlJobHelper.log("任务执行异常:{}", ExceptionUtil.stacktraceToString(e));
```

**日志特点**:
- **实时查看**: 可在XXL-Job管理界面实时查看执行日志
- **异常记录**: 自动记录任务执行异常信息
- **参数追踪**: 记录任务参数和执行状态

## 4. 异步线程池处理机制

### 4.1 线程池架构设计

#### 4.1.1 商品同步线程池
**线程池配置**: [`GoodsSyncTask`](../src/main/java/cc/buyhoo/tax/task/GoodsSyncTask.java#L60)
```java
private final ExecutorService pool = ThreadPoolUtils.newThreadPool();

// 异步处理店铺信息同步
pool.execute(() -> {
    Set<Long> shopUniques = shopList.stream()
        .map(QueryBindShopDto::getShopUnique)
        .collect(Collectors.toSet());
    if (CollUtil.isNotEmpty(shopUniques)) {
        shopFacade.syncShop(queryShops(shopUniques));
    }
});
```

**使用场景**:
- **店铺信息同步**: 异步同步店铺基础信息
- **商品信息同步**: 主线程处理商品同步，避免阻塞
- **性能优化**: 提高商品同步任务的并发处理能力

#### 4.1.2 工具类异步处理
**异步工具**: 使用`ThreadUtil.execAsync()`进行异步处理

**账单统计异步处理**: [`BillStatisticTask`](../src/main/java/cc/buyhoo/tax/task/BillStatisticTask.java#L62)
```java
// 按纳统企业异步统计
for (QueryBranchCompanyDto comp : companyList) {
    ThreadUtil.execAsync(() ->
        busShopBillFacade.statisticBill(comp.getId(), startDate, endDate)
    );
}
```

**监控数据异步写入**: [`SaleListMonitorTask`](../src/main/java/cc/buyhoo/tax/task/SaleListMonitorTask.java#L295)
```java
// 异步更新监控数据
ThreadUtil.execAsync(() ->
    busShopSaleListMonitorFacade.updateSaleListMonitor(listParams)
);
```

### 4.2 异步处理业务场景

#### 4.2.1 多分公司并行处理
**业务需求**: 多个分公司的数据需要并行处理，提高整体处理效率

**实现方式**:
```java
// 遍历分公司，每个分公司异步处理
for (QueryBranchCompanyDto comp : companyList) {
    ThreadUtil.execAsync(() -> {
        // 处理单个分公司的业务逻辑
        processCompanyData(comp);
    });
}
```

**优势**:
- **提高并发性**: 多个分公司数据并行处理
- **避免阻塞**: 主线程不被长时间操作阻塞
- **资源优化**: 合理利用系统资源

#### 4.2.2 耗时操作异步化
**业务场景**: 将耗时的RPC调用和数据处理操作异步化

**典型应用**:
1. **账单统计**: 统计计算耗时较长，异步处理
2. **监控数据写入**: 监控数据量大，异步写入
3. **店铺信息同步**: 店铺数据同步与商品同步并行

### 4.3 异步处理异常管理

#### 4.3.1 异常捕获与记录
**异常处理策略**: 在异步任务中捕获异常并记录

```java
ThreadUtil.execAsync(() -> {
    try {
        // 异步业务逻辑
        busShopBillFacade.statisticBill(comp.getId(), startDate, endDate);
    } catch (Exception e) {
        log.error("账单统计异常，分公司ID:{}", comp.getId(), e);
        // 可选择性地记录到XXL-Job日志
        XxlJobHelper.log("账单统计异常:{}", ExceptionUtil.stacktraceToString(e));
    }
});
```

#### 4.3.2 异步任务监控
**监控策略**: 通过日志和指标监控异步任务执行状态
**失败处理**: 异步任务失败不影响主任务执行，但需要记录和告警

## 5. Nacos服务发现与配置推送

### 5.1 服务注册与发现机制

#### 5.1.1 服务注册配置
**注册中心**: Nacos作为服务注册中心
**配置文件**: [`application.yml`](../src/main/resources/application.yml)

```yaml
spring:
  cloud:
    nacos:
      server-addr: @nacos.server-addr@
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        group: @nacos.tax.group@        # 服务注册组
        namespace: @nacos.namespace@     # 命名空间隔离
```

**启动类配置**: [`StatisticOrderSyncApplication.java`](../src/main/java/cc/buyhoo/tax/StatisticOrderSyncApplication.java)
```java
@EnableDiscoveryClient  // 启用服务发现
@EnableDubbo           // 启用Dubbo服务
```

#### 5.1.2 服务发现流程
**服务注册**: 应用启动时自动向Nacos注册服务实例
**服务发现**: Dubbo通过Nacos发现远程服务提供者
**健康检查**: Nacos定期检查服务实例健康状态

**服务信息**:
- **服务名**: `yxl-tax-statistic-order-sync`
- **端口**: `14003`
- **上下文路径**: `/taxStatisticOrderSync`
- **健康检查**: 自动心跳检测

### 5.2 配置动态推送机制

#### 5.2.1 配置中心架构
**配置来源**: Nacos Config作为配置中心
**配置导入策略**:

```yaml
spring:
  config:
    import:
      - optional:nacos:application.common.yaml      # 通用配置
      - optional:nacos:dubbo-config.yaml           # Dubbo配置
      - optional:nacos:${spring.application.name}.yaml  # 应用专属配置
```

**配置优先级**:
1. Nacos应用专属配置 (最高优先级)
2. Nacos通用配置
3. 本地application.yml配置

#### 5.2.2 动态配置刷新
**刷新注解**: `@RefreshScope`支持配置热更新

**应用示例**:
```java
@Service
@RefreshScope  // 支持配置动态刷新
public class OrderSyncCustomTask {

    @Value("${order.sync.task.pageSize}")
    private Integer orderSyncTaskPageSize;  // 可动态更新的配置
}
```

**配置变更流程**:
1. **配置修改**: 在Nacos控制台修改配置
2. **变更推送**: Nacos推送配置变更事件
3. **自动刷新**: Spring Cloud自动刷新@RefreshScope Bean
4. **生效确认**: 新配置立即生效，无需重启

#### 5.2.3 关键动态配置参数
**订单同步配置**:
- `order.sync.task.pageSize`: 订单同步分页大小
- `xxl.job.admin.addresses`: XXL-Job调度中心地址
- `xxl.job.executor.appname`: 执行器应用名

**Dubbo服务配置**:
- `dubbo.registry.address`: 注册中心地址
- `dubbo.consumer.timeout`: 消费者超时时间
- `dubbo.consumer.retries`: 重试次数

### 5.3 配置变更事件处理

#### 5.3.1 配置监听机制
**自动监听**: Spring Cloud Config自动监听Nacos配置变更
**事件通知**: 配置变更时触发RefreshEvent事件

#### 5.3.2 配置变更影响范围
**任务类配置**: 影响任务执行参数，如分页大小
**连接配置**: 影响数据库连接、RPC连接等
**业务配置**: 影响业务逻辑执行参数

## 6. 异步日志处理机制

### 6.1 Logback异步日志架构

#### 6.1.1 异步Appender配置
**配置文件**: [`logback-common.xml`](../src/main/resources/logback-common.xml)

```xml
<!-- info异步输出 -->
<appender name="async_info" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
    <discardingThreshold>0</discardingThreshold>
    <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
    <queueSize>512</queueSize>
    <!-- 添加附加的appender,最多只能添加一个 -->
    <appender-ref ref="file_info"/>
</appender>

<!-- error异步输出 -->
<appender name="async_error" class="ch.qos.logback.classic.AsyncAppender">
    <discardingThreshold>0</discardingThreshold>
    <queueSize>512</queueSize>
    <appender-ref ref="file_error"/>
</appender>
```

#### 6.1.2 异步日志特性
**队列机制**: 使用内存队列缓存日志事件
**异步写入**: 后台线程异步写入日志文件
**性能优化**: 避免日志I/O阻塞业务线程

**配置特点**:
- **队列大小**: 512，提高日志写入性能
- **不丢失日志**: discardingThreshold=0，确保日志完整性
- **异步处理**: 避免日志I/O影响业务性能

### 6.2 日志分类与路由

#### 6.2.1 日志文件分类
**日志路径**: `logs/yxl-tax-statistic-order-sync/`

**文件分类**:
- **console.log**: 控制台日志，保留1天
- **info.log**: 系统信息日志，保留60天
- **error.log**: 错误日志，保留60天

#### 6.2.2 日志轮转策略
**时间轮转**: 基于时间创建日志文件
**文件命名**: `info.2024-01-01.log`格式
**历史管理**: 自动清理过期日志文件

```xml
<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
    <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
    <maxHistory>60</maxHistory>  <!-- 保留60天 -->
</rollingPolicy>
```

### 6.3 任务执行日志

#### 6.3.1 XXL-Job任务日志
**日志记录**: 通过`XxlJobHelper.log()`记录任务专属日志

**日志示例**:
```java
// 任务开始日志
log.info("------定时任务同步商品信息：开始同步----------------------------");

// 业务异常日志
XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));

// 任务执行异常
XxlJobHelper.log("任务执行异常:{}", ExceptionUtil.stacktraceToString(e));
```

#### 6.3.2 业务操作日志
**同步日志**: 记录数据同步过程和结果
**异常日志**: 记录业务异常和错误信息
**性能日志**: 记录任务执行时间和性能指标

```java
// 性能日志
long millis = DateUtil.between(startTime, DateUtil.date(), DateUnit.MS);
log.info("--------定时任务同步商品信息------------------结束同步--------------------------耗时：{}--",
    DateUtil.formatBetween(millis, BetweenFormatter.Level.MILLISECOND));
```

## 7. 业务流程中的MQ通信模式

### 7.1 订单同步流程通信

#### 7.1.1 百货订单同步通信流程
**流程概述**: [`OrderSyncCustomTask.orderSync()`](../src/main/java/cc/buyhoo/tax/task/OrderSyncCustomTask.java#L95)

```mermaid
graph TD
    A[XXL-Job调度中心] -->|HTTP调度| B[订单同步任务]
    B -->|Dubbo RPC| C[查询分公司服务]
    B -->|Dubbo RPC| D[查询店铺服务]
    B -->|数据库查询| E[本地订单数据]
    B -->|Dubbo RPC| F[同步订单服务]
    B -->|Dubbo RPC| G[同步发票服务]
    B -->|异步线程| H[统计处理]

    subgraph "通信协议"
        I[HTTP - XXL-Job]
        J[Dubbo RPC - 服务调用]
        K[JDBC - 数据库]
        L[ThreadPool - 异步处理]
    end
```

**通信步骤**:
1. **任务调度**: XXL-Job通过HTTP协议调度任务执行
2. **服务查询**: 通过Dubbo RPC查询分公司和店铺信息
3. **数据读取**: 从本地数据库读取订单数据
4. **数据同步**: 通过Dubbo RPC同步订单到远程系统
5. **发票生成**: 通过Dubbo RPC生成发票记录
6. **异步统计**: 使用线程池异步处理统计数据

#### 7.1.2 餐饮订单同步通信流程
**流程概述**: [`CanyinOrderSyncCustomTask.canyinOrderSyncJobHandler()`](../src/main/java/cc/buyhoo/tax/task/CanyinOrderSyncCustomTask.java#L128)

**通信特点**:
- **主子订单结构**: 餐饮订单包含主订单和子订单
- **复杂数据组装**: 需要组装主订单、子订单、支付详情
- **批量同步**: 支持批量同步提高效率

```java
// 餐饮订单同步通信流程
@XxlJob("canyinOrderSyncJobHandler")
public void canyinOrderSyncJobHandler() {
    // 1. 查询分公司信息 (Dubbo RPC)
    Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();

    // 2. 遍历分公司处理
    for (QueryBranchCompanyDto comp : companyList) {
        // 3. 查询餐饮店铺 (Dubbo RPC)
        Result<QueryBindShopResult> shopResult = queryBindCanyinShops(comp.getId());

        // 4. 同步餐饮订单数据 (Dubbo RPC)
        saleListFacade.syncCanyinSaleList(syncParams);

        // 5. 同步退货数据 (Dubbo RPC)
        returnListFacade.syncReturnList(returnSyncParams);
    }
}
```

### 7.2 商品同步流程通信

#### 7.2.1 商品信息同步通信模式
**流程概述**: [`GoodsSyncTask.goodsSync()`](../src/main/java/cc/buyhoo/tax/task/GoodsSyncTask.java#L62)

**异步通信特点**:
- **并行处理**: 店铺同步与商品同步并行执行
- **线程池管理**: 使用专用线程池处理异步任务
- **资源优化**: 避免阻塞主线程

```java
// 商品同步异步通信流程
@XxlJob("goodsSyncJobHandler")
public void goodsSync() {
    // 1. 查询店铺列表 (Dubbo RPC)
    Result<QueryBindShopResult> shopResult = busShopFacade.queryBindShop();

    // 2. 异步同步店铺信息 (线程池 + Dubbo RPC)
    pool.execute(() -> {
        Set<Long> shopUniques = shopList.stream()
            .map(QueryBindShopDto::getShopUnique)
            .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(shopUniques)) {
            shopFacade.syncShop(queryShops(shopUniques));  // Dubbo RPC调用
        }
    });

    // 3. 主线程同步商品信息 (Dubbo RPC)
    for (QueryBindShopDto shop : shopList) {
        List<Goods> goodsList = queryGoods(shop.getShopUnique());
        if (CollUtil.isNotEmpty(goodsList)) {
            goodsFacade.syncGoods(shop.getCompanyId(), shop.getShopUnique(), goodsList);
        }
    }
}
```

### 7.3 监控任务通信流程

#### 7.3.1 订单监控通信模式
**流程概述**: [`SaleListMonitorTask.saleListMonitor()`](../src/main/java/cc/buyhoo/tax/task/SaleListMonitorTask.java#L71)

**通信特点**:
- **数据对比**: 对比本地和远程数据一致性
- **异步写入**: 监控结果异步写入远程系统
- **多数据源**: 同时监控百货和餐饮订单

```java
// 监控任务异步通信
@XxlJob("saleListMonitorJobHandler")
public void saleListMonitor() {
    // 1. 查询分公司 (Dubbo RPC)
    Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();

    for (QueryBranchCompanyDto comp : companyList) {
        // 2. 查询绑定店铺 (Dubbo RPC)
        Result<QueryBindShopResult> shopResult = queryBindShops(comp.getId());

        for (QueryBindShopDto shop : shopList) {
            // 3. 监控数据统计 (本地数据库查询)
            BusSaleListMonitorSyncParams syncParams = buildMonitorParams(shop, startTime, endTime);

            // 4. 异步更新监控数据 (线程池 + Dubbo RPC)
            ThreadUtil.execAsync(() ->
                busShopSaleListMonitorFacade.updateSaleListMonitor(listParams)
            );
        }
    }
}
```

### 7.4 账单统计通信流程

#### 7.4.1 账单统计异步通信
**流程概述**: [`BillStatisticTask.billStatistic()`](../src/main/java/cc/buyhoo/tax/task/BillStatisticTask.java#L38)

**通信特点**:
- **参数化调度**: 支持自定义时间范围参数
- **并行统计**: 多个分公司并行统计
- **无重试策略**: 避免重复统计

```java
// 账单统计异步通信
@XxlJob("billStatisticJobHandler")
public void billStatistic() {
    // 1. 获取任务参数 (XXL-Job参数传递)
    String params = XxlJobHelper.getJobParam();

    // 2. 查询分公司 (Dubbo RPC)
    Result<QueryBranchCompanyResult> branchCompResult = sysCompanyFacade.queryBranchCompany();

    // 3. 异步统计各分公司账单 (线程池 + Dubbo RPC)
    for (QueryBranchCompanyDto comp : companyList) {
        ThreadUtil.execAsync(() ->
            busShopBillFacade.statisticBill(comp.getId(), startDate, endDate)
        );
    }
}
```

## 8. MQ通信异常处理与监控

### 8.1 通信异常处理策略

#### 8.1.1 Dubbo RPC异常处理
**超时处理**: 配置合理的超时时间，避免长时间等待
**重试策略**: 查询类服务允许重试，写入类服务禁用重试
**降级处理**: 关键服务不可用时的降级策略

```java
// RPC异常处理示例
try {
    Result<QueryBindShopResult> shopResult = busShopFacade.queryBindShop();
    if (shopResult.hasFail()) {
        XxlJobHelper.log("查询店铺失败:{}", JSON.toJSONString(shopResult));
        return;  // 任务终止，避免后续错误
    }
} catch (Exception e) {
    log.error("RPC调用异常", e);
    XxlJobHelper.log("RPC调用异常:{}", ExceptionUtil.stacktraceToString(e));
}
```

#### 8.1.2 任务调度异常处理
**任务失败**: XXL-Job自动记录任务执行失败
**异常日志**: 详细记录异常堆栈信息
**告警机制**: 可配置任务失败告警

#### 8.1.3 异步任务异常处理
**异常隔离**: 异步任务异常不影响主任务
**异常记录**: 记录异步任务执行异常
**监控告警**: 监控异步任务执行状态

```java
// 异步任务异常处理
ThreadUtil.execAsync(() -> {
    try {
        busShopBillFacade.statisticBill(comp.getId(), startDate, endDate);
    } catch (Exception e) {
        log.error("异步账单统计失败，分公司ID:{}", comp.getId(), e);
        // 可选择性地发送告警通知
    }
});
```

### 8.2 通信性能监控

#### 8.2.1 RPC调用监控
**调用耗时**: 监控Dubbo RPC调用耗时
**成功率**: 监控RPC调用成功率
**并发量**: 监控并发RPC调用数量

#### 8.2.2 任务执行监控
**执行时间**: 记录任务总执行时间
**数据量**: 记录处理的数据量
**成功率**: 监控任务执行成功率

```java
// 任务性能监控
Date startTime = DateUtil.date();
try {
    // 执行任务逻辑
    processTaskLogic();
} finally {
    long millis = DateUtil.between(startTime, DateUtil.date(), DateUnit.MS);
    log.info("任务执行完成，耗时：{}", DateUtil.formatBetween(millis, BetweenFormatter.Level.MILLISECOND));
}
```

#### 8.2.3 异步队列监控
**队列长度**: 监控线程池队列长度
**线程状态**: 监控线程池活跃线程数
**任务积压**: 监控异步任务积压情况

## 9. 总结

### 9.1 MQ架构特点总结

本系统的"MQ"架构具有以下特点：

1. **Dubbo RPC通信**: 作为主要的服务间通信方式，提供高性能的远程调用
2. **XXL-Job任务调度**: 提供分布式任务调度能力，支持参数传递和日志记录
3. **异步线程池**: 提供本地异步处理能力，提高系统并发性能
4. **Nacos服务发现**: 提供服务注册发现和配置动态推送能力
5. **异步日志处理**: 提供高性能的异步日志写入能力

### 9.2 业务流程通信模式

**核心通信模式**:
- **同步RPC + 异步处理**: 主要业务模式，RPC调用同步，业务处理异步
- **任务调度 + 服务调用**: XXL-Job调度任务，任务内部通过RPC调用服务
- **配置推送 + 动态刷新**: Nacos推送配置变更，应用动态刷新配置
- **异步日志 + 实时监控**: 异步记录日志，实时监控系统状态

### 9.3 性能与可靠性保障

**性能优化**:
- **异步处理**: 通过线程池和异步工具提高并发处理能力
- **批量操作**: 支持批量数据同步，提高处理效率
- **连接复用**: Dubbo连接池复用，减少连接开销

**可靠性保障**:
- **重试策略**: 合理配置重试策略，平衡可靠性和性能
- **异常处理**: 完善的异常处理机制，确保系统稳定运行
- **监控告警**: 全面的监控指标和告警机制

### 9.4 扩展性与维护性

**扩展性**:
- **服务化架构**: 基于Dubbo的微服务架构，易于扩展
- **配置外部化**: 配置集中管理，支持动态调整
- **任务可配置**: 任务调度时间和参数可灵活配置

**维护性**:
- **日志完善**: 详细的日志记录，便于问题排查
- **监控全面**: 全面的性能监控和业务监控
- **文档完整**: 完整的技术文档和业务文档

---

*本文档基于项目当前版本编写，如有更新请及时维护文档内容。*
