# 税务统计订单同步系统

## 项目简介

`yxl-tax-statistic-order-sync` 是一个基于 Spring Boot 的微服务应用，主要负责从不同业务系统（百货收银系统、餐饮系统）同步订单数据到税务统计系统，为税务申报和统计分析提供数据支撑。

## 主要功能

- **百货订单同步**: 同步百货收银系统的销售订单和退货单数据
- **餐饮订单同步**: 同步餐饮系统的主子订单结构数据
- **商品信息同步**: 同步商品基础信息和店铺信息
- **账单统计**: 统计服务费和结算数据
- **订单监控**: 监控订单数据异常和同步状态

## 技术栈

- **框架**: Spring Boot 2.x
- **服务发现**: Nacos
- **RPC通信**: Dubbo
- **数据库**: MySQL
- **ORM**: MyBatis Plus
- **任务调度**: XXL-Job
- **配置管理**: Nacos Config

## 快速开始

### 环境要求

- JDK 8+
- MySQL 5.7+
- Nacos 服务注册中心
- XXL-Job 调度中心

### 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 配置说明

- 服务端口: `14003`
- 上下文路径: `/taxStatisticOrderSync`
- 配置文件: `src/main/resources/application.yml`

## 文档

📖 **[业务流程文档](docs/BIZ.md)** - 详细的业务流程、数据模型和代码说明

📊 **[数据库ER图](docs/ER.md)** - 数据库表结构及关系

💾 **[I/O业务逻辑](docs/IO.md)** - 文件读取/写入/存储相关业务逻辑

🔄 **[MQ消息队列](docs/MQ.md)** - Dubbo RPC、XXL-Job任务调度、异步处理等MQ相关业务逻辑

## 主要任务

| 任务名称 | 描述 | 调度器标识 |
|---------|------|-----------|
| 商品同步 | 同步商品和店铺基础信息 | `goodsSyncJobHandler` |
| 百货订单同步 | 同步百货收银系统订单数据 | `orderSyncCustomJobHandler` |
| 餐饮订单同步 | 同步餐饮系统订单数据 | `canyinOrderSyncJobHandler` |
| 账单统计 | 统计服务费和结算数据 | `billStatisticJobHandler` |
| 订单监控 | 监控订单数据异常 | `saleListMonitorJobHandler` |
| 数据清理 | 清空测试数据 | `truncateOrderData` |

## 项目结构

```
src/main/java/cc/buyhoo/tax/
├── StatisticOrderSyncApplication.java  # 主启动类
├── dao/                                # 数据访问层
├── entity/                             # 实体类
├── task/                               # 任务调度类
├── params/                             # 参数类
└── result/                             # 结果类
```

## 贡献

如需贡献代码或报告问题，请参考业务流程文档了解项目详情。

## 许可证

本项目为内部项目，请遵守公司相关规定。

---

*更多详细信息请查看项目文档：[业务流程](docs/BIZ.md) | [数据库ER图](docs/ER.md) | [I/O逻辑](docs/IO.md) | [MQ通信](docs/MQ.md)*
